package com.dvag.codingex.scrabble

import com.dvag.codingex.scrabble.provided.Tile
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*

class TileBagTest {

    private val tileBag = TileBag()

    @Test
    fun takeRandomTiles() {
        val initialSize = tileBag.remainingTiles()
        val tiles = tileBag.takeRandomTiles(7)

        assertEquals(7, tiles.size)
        assertEquals(initialSize - 7, tileBag.remainingTiles())
    }

    @Test
    fun checkDistribution() {
        val tiles = mutableListOf<Tile>()

        repeat(14) {
            tiles.addAll(tileBag.takeRandomTiles(7))
        }
        tiles.addAll(tileBag.takeRandomTiles(4))

        assertEquals(0, tileBag.remainingTiles())
        Tile.entries.forEach {
            assertEquals(it.quantity, tiles.count { tile -> tile == it })
        }
    }
}