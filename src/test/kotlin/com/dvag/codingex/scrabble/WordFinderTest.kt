package com.dvag.codingex.scrabble

import com.dvag.codingex.scrabble.provided.Dictionary
import com.dvag.codingex.scrabble.provided.Dictionary.Companion.dictionaryLines
import com.dvag.codingex.scrabble.provided.Dictionary.Match.EXACT
import com.dvag.codingex.scrabble.provided.Tile
import com.dvag.codingex.scrabble.provided.Tile.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class WordFinderTest {

    private val wordFinder = WordFinder()

    private val dictionary = Dictionary()

    @Test
    fun `Guess 100 words`() {
        val existingWords = dictionaryLines()
            .map {
                it.toCharArray().map { char -> Tile.valueOf(char.toString()) }
            }
        repeat(100) {
            val tiles = existingWords.random()
            val word = wordFinder.findBestWord(tiles)

            assertTrue(dictionary.hasMatch(word.first!!) == EXACT)
        }
    }

    @Test
    fun `Guess the best word`() {
        var word = wordFinder.findBestWord(listOf(S, P, A, S, S, F, G))

        assertEquals(Pair(listOf(S, P, A, S, S), 8), word)

        word = wordFinder.findBestWord(listOf(A, F, S, S, S, P, G))

        assertEquals(Pair(listOf(S, P, A, S, S), 8), word)

        word = wordFinder.findBestWord(listOf(E, S, I, E, R, N, A))

        assertEquals(Pair(listOf(A, N, R, E, I, S, E), 7), word)
    }

    @Test
    fun guessWordWithJoker() {
        var word = wordFinder.findBestWord(listOf(S, P, Joker, S, S, F, G))

        assertEquals(Pair(listOf(S, P, A, S, S), 8), word)

        word = wordFinder.findBestWord(listOf(S, Joker, Joker, S, S, F, G))

        assertEquals(Pair(listOf(F, A, X), 13), word)
    }

    @Test
    fun rackWithJokersReplaced() {
        var rackReplaced = wordFinder.rackWithJokersReplaced(listOf(A, Joker, B))

        assertTrue(rackReplaced.contains(listOf(A, A, B)))
        assertTrue(rackReplaced.contains(listOf(A, Z, B)))
        assertFalse(rackReplaced.contains(listOf(A, Joker, B)))
        rackReplaced.forEach {
            assertFalse(it.contains(Joker))
        }

        rackReplaced = wordFinder.rackWithJokersReplaced(listOf(A, C, B, D))

        assertEquals(listOf(A, C, B, D), rackReplaced[0])
    }
}