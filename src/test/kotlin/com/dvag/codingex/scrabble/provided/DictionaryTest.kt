package com.dvag.codingex.scrabble.provided

import com.dvag.codingex.scrabble.provided.Dictionary.Match.*
import com.dvag.codingex.scrabble.provided.Tile.*
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*

class DictionaryTest {

    private val dictionary = Dictionary()

    @Test
    fun isKnownWord() {
        assertEquals(EXACT, dictionary.hasMatch(listOf(P, U, N, K, T)))

        assertEquals(NONE, dictionary.hasMatch(listOf(P, U, N, K, T, Z)))
    }

    @Test
    fun hasWordWithPrefix() {
        assertEquals(PREFIX, dictionary.hasMatch(listOf(P)))
        assertEquals(PREFIX, dictionary.hasMatch(listOf(P, U, N)))
        assertEquals(PREFIX, dictionary.hasMatch(listOf(P, U, N, K)))
        assertEquals(EXACT, dictionary.hasMatch(listOf(P, U, N, K, T)))
    }

    @Test
    fun jokerThrows() {
        assertThrows(IllegalArgumentException::class.java) {
            dictionary.hasMatch(listOf(Q, Joker, A, L))
        }
    }
}