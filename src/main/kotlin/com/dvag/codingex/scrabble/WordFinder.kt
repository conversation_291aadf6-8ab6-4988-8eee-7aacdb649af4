package com.dvag.codingex.scrabble

import com.dvag.codingex.scrabble.provided.Dictionary
import com.dvag.codingex.scrabble.provided.Tile

class WordFinder {

    private val dictionary: Dictionary = Dictionary()
    private val scoreCalculator = ScoreCalculator()

    private var  results:
    /**
     * Guesses a word that can be formed from the given rack,
     * e.g. for the rack [R, E, S, U, P] it might return the pair ([S, U, P, E, R], 8).
     * TODO: Find a simple but efficient algorithm which guesses the best word for the given rack.
     * TODO: Remember that Joker tiles can be used as any letter.
     */
    fun findBestWord(rack: List<Tile>): Pair<List<Tile>?, Int> {

        var usedTiles: BooleanArray= BooleanArray(rack.size)
        var currentWord = mutableListOf<Tile>()
        depthFirstSearch(rack,usedTiles, currentWord);

        // Now we have all the best word



    }


    fun depthFirstSearch(rack: List<Tile>, usedTiles: BooleanArray, currentWord: List<Tile>): Pair<List<Tile>?, Int> {

        if(dictionary.hasMatch(currentWord) == Dictionary.Match.EXACT){
            results.add(currentWord)
        }

        for(  i in 0..rack.size-1){
            val tmpWord = currentWord.toMutableList()
            if( usedTiles[i])
                continue

            usedTiles[i]=true

            tmpWord.add(rack[i])

            depthFirstSearch(rack, usedTiles, tmpWord)
            // Backtrack so we can use the tile again
            usedTiles[i]=false
       }


    }

    /**
     * Returns all variants of the given rack
     * where the jokers are replaced by all possible letters.
     */
    fun rackWithJokersReplaced(rack: List<Tile>): List<List<Tile>> {
        throw NotImplementedError("Please implement the WordFinder.rackWithJokersReplaced() method")
    }

}