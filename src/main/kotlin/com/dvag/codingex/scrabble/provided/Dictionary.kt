package com.dvag.codingex.scrabble.provided

import java.io.BufferedReader
import java.io.InputStreamReader

class Dictionary {

    private val dictionary: Trie

    init {
        this.dictionary = readDictionary()
    }

    companion object {
        fun dictionaryLines(): List<String> {
            with(Dictionary::class.java.getResourceAsStream("/dictionary.txt")) {
                with(BufferedReader(InputStreamReader(this!!))) {
                    return this.readLines()
                        .map { it.trim()
                            .replace("ß", "ss")
                            .replace(".", "")
                            .uppercase() }
                        .filter { it.length <= 7}
                        .filter { it.length >= 3}
                        .toList()
                }
            }
        }
    }

    /**
     * Checks if there is a word in the dictionary that starts with the given prefix (e.g. "Quali", "Qualit", "Qualität").
     */
    fun hasMatch(prefix: List<Tile>): Match {
        if (prefix.contains(Tile.Joker)) throw IllegalArgumentException("Joker tiles are not supported")
        return dictionary.search(prefix.joinToString(""))
    }

    private fun readDictionary(): Trie {
        val trie = Trie()
        dictionaryLines().forEach { trie.insert(it) }
        return trie
    }

    private class TrieNode {
        val children: MutableMap<Char, TrieNode> = mutableMapOf()
        var isEndOfWord: Boolean = false
    }

    enum class Match {
        EXACT, PREFIX, NONE
    }

    /**
     * A simple trie implementation to store the dictionary.
     * Supports prefix searching and exact word searching.
     */
    private class Trie {
        private val root = TrieNode()

        fun insert(word: String) {
            var current = root
            for (char in word) {
                current = current.children.getOrPut(char) { TrieNode() }
            }
            current.isEndOfWord = true
        }

        fun search(word: String): Match {
            var current = root
            for (char in word) {
                current = current.children[char] ?: return Match.NONE
            }
            return when (current.isEndOfWord) {
                true -> Match.EXACT
                false -> Match.PREFIX
            }
        }
    }
}