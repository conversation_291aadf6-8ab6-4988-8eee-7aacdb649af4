package com.dvag.codingex.scrabble.provided

/**
 * Scrabble tiles with their scores
 * and quantities.
 */
enum class Tile(val score: Int, val quantity: Int) {
    A(1, 5),
    B(3, 2),
    C(4, 2),
    <PERSON>(1, 4),
    <PERSON>(1, 15),
    <PERSON>(4, 2),
    <PERSON>(2, 3),
    <PERSON>(2, 4),
    <PERSON>(1, 6),
    <PERSON>(6, 1),
    <PERSON>(4, 2),
    <PERSON>(2, 3),
    <PERSON>(3, 4),
    N(1, 9),
    O(2, 3),
    P(4, 1),
    Q(10, 1),
    R(1, 6),
    S(1, 7),
    T(1, 6),
    U(1, 6),
    <PERSON>(6, 1),
    <PERSON>(3, 1),
    <PERSON>(8, 1),
    <PERSON>(10, 1),
    <PERSON>(3, 1),
    <PERSON>(6, 1),
    <PERSON>(8, 1),
    <PERSON>(6, 1),
    <PERSON>(0, 2);

    override fun toString(): String {
        if (this == Joker) {
            return "_"
        }
        return super.toString()
    }
}