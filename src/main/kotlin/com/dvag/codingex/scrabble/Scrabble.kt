package com.dvag.codingex.scrabble

import com.dvag.codingex.scrabble.provided.Tile

class Scrabble {

    private val wordFinder = WordFinder()

    fun playOneRound() {
        val tileBag = TileBag()
        val tilesPlayer1 = tileBag.takeRandomTiles(7)
        val tilesPlayer2 = tileBag.takeRandomTiles(7)
        println("Player 1's tiles:")
        renderScrabbleTile(tilesPlayer1)
        println("Player 2's tiles:")
        renderScrabbleTile(tilesPlayer2)

        val resultPlayer1 = wordFinder.findBestWord(tilesPlayer1)
        val resultPlayer2 = wordFinder.findBestWord(tilesPlayer2)

        println("Player 1's result:")
        printResult(resultPlayer1)
        println("Player 2's result:")
        printResult(resultPlayer2)
        if (resultPlayer1.second > resultPlayer2.second) {
            println("Player 1 wins")
        } else if (resultPlayer1.second < resultPlayer2.second) {
            println("Player 2 wins")
        } else {
            println("It's a draw")
        }
    }

    private fun printResult(resultPlayer1: Pair<List<Tile>?, Int>) {
        if (resultPlayer1.first != null) {
            println("Best word with score ${resultPlayer1.second}:")
            renderScrabbleTile(resultPlayer1.first!!)
        } else {
            println("No word found")
        }
    }

    fun renderScrabbleTile(tiles: List<Tile>) {
        val horizontalBorder = "+---+".repeat(tiles.size)
        val emptyLine = "|   |".repeat(tiles.size)

        println(horizontalBorder)
        println(emptyLine)
        tiles.forEach { character ->
            print("| $character |")
        }
        println()
        println(emptyLine)
        println(horizontalBorder)
    }
}

fun main() {
    val scrabble = Scrabble()
    repeat(10) {
        println()
        println("******************************************")
        scrabble.playOneRound()
        println("******************************************")
        println()
    }
}