package com.dvag.codingex.scrabble

import com.dvag.codingex.scrabble.provided.Tile

/**
 * Models the bag containing tiles.
 */
class TileBag {

    /**
     * Character => remaining tiles
     */
    private val characters: MutableMap<Tile, Int> = Tile.entries.associateWith { it.quantity }.toMutableMap()

    /**
     * Take random tiles from the bag.
     */
    fun takeRandomTiles(quantity: Int): List<Tile> {
        throw NotImplementedError("Please implement the TileBag.takeRandomTiles() method")
    }

    /**
     * Returns the number of remaining tiles.
     */
    fun remainingTiles(): Int {
        return characters.values.sum()
    }
}