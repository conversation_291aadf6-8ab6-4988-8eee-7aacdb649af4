# Wortspiel (Scrabble)

![scrabble](./scrabble.png)

## Hintergrund
Bei diesem Spiel versuchen die Spieler, aus einem Satz von Buchstabenplättchen Wörter zu bilden.
Den verschiedenen Buchstaben sind unterschiedliche Punkte zugewiesen.

Im deutschen Alphabet haben die Buchstaben die folgenden Punktzahlen:

| Punkt(e) | Buchstabe(n)              |
|----------|---------------------------|
| 1        | A, D, E, I, N, R, S, T, U |
| 2        | G, H, L, O                |
| 3        | B, M, W, Z                |
| 4        | C, F, K, P                |
| 6        | Ä, J, Ü, V                |
| 8        | Ö, X                      |
| 10       | Q, Y                      |
| 0        | Joker / Blanko            |

Das „ß“ gibt es beim Scrabble nicht und muss durch zwei „s“ ersetzt werden.

### Beispiel
Das Wort "QUALITÄT" hat einen Wert von 23:

```
QUALITÄT = 10 + 1 + 1 + 2 + 1 + 1 + 6 + 1 = 23
```

### Verteilung der Buchstaben
Die Buchstaben im Beutel haben die folgende Verteilung:

| Anzahl der Kacheln | Buchstabe(n)                 |
|--------------------|------------------------------|
| 15                 | E                            |
| 9                  | N                            |
| 7                  | S                            |
| 6                  | I, R, T, U                   |
| 5                  | A                            |
| 4                  | D                            |
| 4                  | M                            |
| 3                  | G, H, L, O                   |
| 2                  | B, C, F, K                   |
| 1                  | Ä, J, Ö, Ü, V, W, Z, X, Q, Y |
| 2                  | Joker / Blanko               |

Die 2 Joker können einen beliebigen anderen Buchstaben ersetzen, geben dafür aber auch keine Punkte.

## Eine Runde spielen
Sie können die `main` Funktion in der [`Scrabble`](./src/main/kotlin/com/dvag/codingex/scrabble/Scrabble.kt) Klasse verwenden, um eine Runde zu spielen.

## Aufgaben

###  1. Implementieren Sie die Funktion zum Berechnen der Punktzahl
Berechnen Sie die Punktzahl für ein Wort im [`ScoreCalculator`](./src/main/kotlin/com/dvag/codingex/scrabble/ScoreCalculator.kt). Die Punktzahl ist die Summe der Punkte für die Buchstaben, aus denen ein Wort besteht.
Die Tests in [`ScoreCalculatorTest`](./src/test/kotlin/com/dvag/codingex/scrabble/ScoreCalculatorTest.kt) sollten grün sein.

### 2. Implementieren Sie die Funktion zum Ziehen der Kacheln
Legen Sie sieben zufällig ausgewählte Kacheln aus dem deutschen Alphabet in die Ablage eines Spielers. Im echten Spiel werden die Spielsteine nach dem Zufallsprinzip aus einem Beutel gezogen, der eine feste Anzahl von Spielsteinen enthält. Implementieren Sie die Funktionen im [`TileBag`](./src/main/kotlin/com/dvag/codingex/scrabble/TileBag.kt).
Die Tests in [`TileBagTest`](./src/test/kotlin/com/dvag/codingex/scrabble/TileBagTest.kt) sollten grün sein.

### 3. Das Wort mit der höchsten Punktzahl finden (ohne Joker)
Finden Sie das Wort mit der höchsten Punktzahl, welches aus den sieben Spielsteinen gebildet wird.
Gültige Wörter müssen im Wörterbuch enthalten sein und dürfen nur die sieben Spielsteine verwenden.
Als Vereinfachung gehen wir zunächst davon aus, dass es keine Joker gibt. 
Implementieren Sie die Funktionen im [`WordFinder`](./src/main/kotlin/com/dvag/codingex/scrabble/WordFinder.kt).
Einige Tests in [`WordFinderTest`](./src/test/kotlin/com/dvag/codingex/scrabble/WordFinderTest.kt) sollten grün sein.

### 4. Das Wort mit der höchsten Punktzahl finden (mit Jokern)
Jetzt wirds schwieriger und wir führen Joker hinzu. Ein Joker kann für jeden Buchstaben stehen, aber er gibt keine Punkte.
Passen Sie die Logig in [`WordFinder`](./src/main/kotlin/com/dvag/codingex/scrabble/WordFinder.kt) an, um auch Joker zu berücksichtigen.
Die Tests in [`WordFinderTest`](./src/test/kotlin/com/dvag/codingex/scrabble/WordFinderTest.kt) sollten grün sein.